const Layout = () => import("@/layout/index.vue");

export default {
  path: "/admin",
  name: "Admin",
  component: Layout,
  redirect: "/admin/list",
  meta: {
    icon: "ep/user-filled",
    title: "管理员管理",
    rank: 10
  },
  children: [
    {
      path: "/admin/list",
      name: "AdminList",
      component: () => import("@/views/admin/list/index.vue"),
      meta: {
        title: "管理员列表",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
