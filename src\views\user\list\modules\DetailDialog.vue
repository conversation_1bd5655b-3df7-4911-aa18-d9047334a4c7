<template>
  <el-dialog
    v-model="visible"
    title="用户详情"
    width="700px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户头像">
          <el-avatar :size="60" :src="data.avatar" />
        </el-descriptions-item>
        <el-descriptions-item label="昵称">
          {{ data.nickname }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ data.phone }}
        </el-descriptions-item>
        <el-descriptions-item label="等级">
          {{ data.level }}级 - {{ data.levelName }}
        </el-descriptions-item>
        <el-descriptions-item label="冥想天数">
          {{ data.meditationDays }}天
        </el-descriptions-item>
        <el-descriptions-item label="总冥想时长">
          {{ Math.floor(data.totalMeditationTime / 60) }}分钟
        </el-descriptions-item>
        <el-descriptions-item label="多肉等级">
          {{ data.succulentLevel }}级
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.status === 1 ? 'success' : 'danger'">
            {{ data.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">
          {{ data.registerTime }}
        </el-descriptions-item>
        <el-descriptions-item label="最后登录">
          {{ data.lastLoginTime }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface User {
  id: number;
  avatar: string;
  nickname: string;
  phone: string;
  level: number;
  levelName: string;
  meditationDays: number;
  totalMeditationTime: number;
  succulentLevel: number;
  registerTime: string;
  lastLoginTime: string;
  status: number;
}

interface Props {
  modelValue: boolean;
  data?: User | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}
</style>
