const Layout = () => import("@/layout/index.vue");

export default {
  path: "/meditation",
  name: "Meditation",
  component: Layout,
  redirect: "/meditation/content",
  meta: {
    icon: "ep/headset",
    title: "冥想内容管理",
    rank: 2
  },
  children: [
    {
      path: "/meditation/content",
      name: "MeditationContent",
      component: () => import("@/views/meditation/content/index.vue"),
      meta: {
        title: "内容管理",
        showLink: true
      }
    },
    {
      path: "/meditation/category",
      name: "MeditationCategory",
      component: () => import("@/views/meditation/category/index.vue"),
      meta: {
        title: "分类管理",
        showLink: true
      }
    },
    {
      path: "/meditation/tag",
      name: "MeditationTag",
      component: () => import("@/views/meditation/tag/index.vue"),
      meta: {
        title: "标签管理",
        showLink: true
      }
    },
    {
      path: "/meditation/course",
      name: "MeditationCourse",
      component: () => import("@/views/meditation/course/index.vue"),
      meta: {
        title: "课程管理",
        showLink: true
      }
    },
    {
      path: "/meditation/audit",
      name: "MeditationAudi<PERSON>",
      component: () => import("@/views/meditation/audit/index.vue"),
      meta: {
        title: "内容审核",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
