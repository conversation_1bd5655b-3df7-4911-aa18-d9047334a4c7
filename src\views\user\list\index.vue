<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";

defineOptions({
  name: "UserList"
});

interface User {
  id: number;
  avatar: string;
  nickname: string;
  phone: string;
  level: number;
  levelName: string;
  meditationDays: number;
  totalMeditationTime: number;
  succulentLevel: number;
  registerTime: string;
  lastLoginTime: string;
  status: number;
}

const loading = ref(false);
const tableData = ref<User[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});
const detailDialogVisible = ref(false);
const currentRow = ref<User | null>(null);

const searchForm = reactive({
  nickname: "",
  phone: "",
  level: "",
  status: ""
});

// 模拟数据
const mockData: User[] = [
  {
    id: 1,
    avatar:
      '<img width="80" height="80" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png">',
    nickname: "冥想小白",
    phone: "138****1234",
    level: 1,
    levelName: "初心者",
    meditationDays: 15,
    totalMeditationTime: 450,
    succulentLevel: 2,
    registerTime: "2024-01-15 10:30:00",
    lastLoginTime: "2024-01-20 09:15:00",
    status: 1
  },
  {
    id: 2,
    avatar:
      '<img width="80" height="80" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png">',
    nickname: "静心达人",
    phone: "139****5678",
    level: 3,
    levelName: "进阶者",
    meditationDays: 45,
    totalMeditationTime: 1350,
    succulentLevel: 5,
    registerTime: "2023-12-01 14:20:00",
    lastLoginTime: "2024-01-20 20:30:00",
    status: 1
  }
];

const levelOptions = [
  { label: "初心者", value: 1 },
  { label: "进阶者", value: 2 },
  { label: "专家", value: 3 },
  { label: "大师", value: 4 }
];

const statusOptions = [
  { label: "正常", value: 1 },
  { label: "禁用", value: 0 }
];

const columns = [
  {
    label: "用户头像",
    slot: "content",
    minWidth: 80
  },
  {
    label: "昵称",
    prop: "nickname",
    minWidth: 120
  },
  {
    label: "手机号",
    prop: "phone",
    minWidth: 120
  },
  {
    label: "冥想等级",
    prop: "levelName",
    minWidth: 100
  },
  {
    label: "冥想天数",
    prop: "meditationDays",
    minWidth: 100
  },
  {
    label: "总冥想时长(分钟)",
    prop: "totalMeditationTime",
    minWidth: 140
  },
  {
    label: "多肉等级",
    prop: "succulentLevel",
    minWidth: 100
  },
  {
    label: "注册时间",
    prop: "registerTime",
    minWidth: 160
  },
  {
    label: "最后登录",
    prop: "lastLoginTime",
    minWidth: 160
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

const onSearch = () => {
  loading.value = true;
  // 模拟搜索
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (
        (!searchForm.nickname || item.nickname.includes(searchForm.nickname)) &&
        (!searchForm.phone || item.phone.includes(searchForm.phone)) &&
        (!searchForm.level || item.level.toString() === searchForm.level) &&
        (!searchForm.status || item.status.toString() === searchForm.status)
      );
    });
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.nickname = "";
  searchForm.phone = "";
  searchForm.level = "";
  searchForm.status = "";
  onSearch();
};

const handleEdit = (row: User) => {
  ElMessage.info(`编辑用户: ${row.nickname}`);
};

const handleToggleStatus = (row: User) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}用户 ${row.nickname} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const handleViewDetail = (row: User) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

const handleDelete = (row: User) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.nickname} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    // 暂时只实现样式，不实际删除
    ElMessage.success("删除成功（演示）");
  });
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  onSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  onSearch();
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="昵称：" prop="nickname">
        <el-input
          v-model="searchForm.nickname"
          placeholder="请输入用户昵称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="手机号：" prop="phone">
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="等级：" prop="level">
        <el-select
          v-model="searchForm.level"
          placeholder="请选择等级"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="用户列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button type="primary" :icon="useRenderIcon('ep:download')">
          导出数据
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #content="{ row }">
            <span v-html="row.avatar" />
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "正常" : "禁用" }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
