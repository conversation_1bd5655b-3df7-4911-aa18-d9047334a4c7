<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";

defineOptions({
  name: "MeditationTag"
});

interface Tag {
  id: number;
  name: string;
  color: string;
  description: string;
  useCount: number;
  status: number;
  createTime: string;
}

const loading = ref(false);
const tableData = ref<Tag[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const detailDialogVisible = ref(false);
const currentRow = ref<Tag | null>(null);

const formData = reactive({
  id: 0,
  name: "",
  color: "#409EFF",
  description: "",
  status: 1
});

const searchForm = reactive({
  name: "",
  status: ""
});

// 模拟数据
const mockData: Tag[] = [
  {
    id: 1,
    name: "放松",
    color: "#67C23A",
    description: "帮助身心放松的内容",
    useCount: 45,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    name: "睡眠",
    color: "#909399",
    description: "改善睡眠质量",
    useCount: 32,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 3,
    name: "正念",
    color: "#E6A23C",
    description: "正念冥想相关",
    useCount: 28,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 4,
    name: "专注",
    color: "#F56C6C",
    description: "提升专注力",
    useCount: 21,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 5,
    name: "晨间",
    color: "#409EFF",
    description: "晨间冥想",
    useCount: 18,
    status: 1,
    createTime: "2024-01-01 10:00:00"
  }
];

const statusOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 }
];

const colorOptions = [
  "#409EFF",
  "#67C23A",
  "#E6A23C",
  "#F56C6C",
  "#909399",
  "#FF6B6B",
  "#4ECDC4",
  "#45B7D1",
  "#96CEB4",
  "#FFEAA7"
];

const columns = [
  {
    label: "标签名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "颜色",
    prop: "color",
    minWidth: 100
  },
  {
    label: "描述",
    prop: "description",
    minWidth: 200
  },
  {
    label: "使用次数",
    prop: "useCount",
    minWidth: 100
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "创建时间",
    prop: "createTime",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (
        (!searchForm.name || item.name.includes(searchForm.name)) &&
        (!searchForm.status || item.status.toString() === searchForm.status)
      );
    });
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.name = "";
  searchForm.status = "";
  onSearch();
};

const handleAdd = () => {
  dialogTitle.value = "新增标签";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

const handleDetail = (row: Tag) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

const handleEdit = (row: Tag) => {
  dialogTitle.value = "编辑标签";
  isEdit.value = true;
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    color: row.color,
    description: row.description,
    status: row.status
  });
  dialogVisible.value = true;
};

const handleDelete = (row: Tag) => {
  if (row.useCount > 0) {
    ElMessage.warning("该标签正在使用中，无法删除");
    return;
  }

  ElMessageBox.confirm(`确定要删除标签 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    // 暂时只实现样式，不实际删除
    ElMessage.success("删除成功（演示）");
  });
};

const handleToggleStatus = (row: Tag) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}标签 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const resetFormData = () => {
  formData.id = 0;
  formData.name = "";
  formData.color = "#409EFF";
  formData.description = "";
  formData.status = 1;
};

const handleSubmit = () => {
  if (!formData.name) {
    ElMessage.warning("请输入标签名称");
    return;
  }

  if (isEdit.value) {
    const index = tableData.value.findIndex(item => item.id === formData.id);
    if (index > -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        ...formData
      };
      ElMessage.success("编辑成功");
    }
  } else {
    const newTag: Tag = {
      ...formData,
      id: Date.now(),
      useCount: 0,
      createTime: new Date().toLocaleString()
    };
    tableData.value.push(newTag);
    ElMessage.success("新增成功");
  }

  dialogVisible.value = false;
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="标签名称：" prop="name">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入标签名称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="标签管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增标签
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #color="{ row }">
            <div class="flex items-center">
              <div
                class="w-4 h-4 rounded mr-2"
                :style="{ backgroundColor: row.color }"
              />
              <span>{{ row.color }}</span>
            </div>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      draggable
    >
      <el-form :model="formData" label-width="100px" label-position="right">
        <el-form-item label="标签名称" required>
          <el-input
            v-model="formData.name"
            placeholder="请输入标签名称"
            maxlength="10"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="标签颜色">
          <div class="flex items-center">
            <el-color-picker v-model="formData.color" />
            <div class="ml-4 flex flex-wrap gap-2">
              <div
                v-for="color in colorOptions"
                :key="color"
                class="w-6 h-6 rounded cursor-pointer border-2"
                :class="
                  formData.color === color
                    ? 'border-gray-400'
                    : 'border-gray-200'
                "
                :style="{ backgroundColor: color }"
                @click="formData.color = color"
              />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="标签描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入标签描述"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
