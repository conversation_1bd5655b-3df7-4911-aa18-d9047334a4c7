<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";

defineOptions({
  name: "SucculentVariety"
});

interface Variety {
  id: number;
  name: string;
  scientificName: string;
  category: string;
  rarity: string;
  image: string;
  description: string;
  growthCycle: number;
  maxLevel: number;
  baseEnergy: number;
  unlockCondition: string;
  attributes: {
    beauty: number;
    hardiness: number;
    growth: number;
  };
  status: number;
  createTime: string;
}

const loading = ref(false);
const tableData = ref<Variety[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const detailDialogVisible = ref(false);
const currentRow = ref<Variety | null>(null);

const formData = reactive({
  id: 0,
  name: "",
  scientificName: "",
  category: "景天科",
  rarity: "普通",
  image: "",
  description: "",
  growthCycle: 30,
  maxLevel: 10,
  baseEnergy: 100,
  unlockCondition: "",
  attributes: {
    beauty: 50,
    hardiness: 50,
    growth: 50
  },
  status: 1
});

const searchForm = reactive({
  name: "",
  category: "",
  rarity: "",
  status: ""
});

// 模拟数据
const mockData: Variety[] = [
  {
    id: 1,
    name: "玉露",
    scientificName: "Haworthia cooperi",
    category: "百合科",
    rarity: "稀有",
    image:
      "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    description: "晶莹剔透的多肉植物，叶片如玉石般透明",
    growthCycle: 45,
    maxLevel: 15,
    baseEnergy: 200,
    unlockCondition: "冥想等级达到3级",
    attributes: {
      beauty: 90,
      hardiness: 60,
      growth: 40
    },
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 2,
    name: "胧月",
    scientificName: "Graptopetalum paraguayense",
    category: "景天科",
    rarity: "普通",
    image:
      "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    description: "叶片肥厚，颜色会随光照变化",
    growthCycle: 30,
    maxLevel: 10,
    baseEnergy: 100,
    unlockCondition: "新手即可获得",
    attributes: {
      beauty: 70,
      hardiness: 80,
      growth: 70
    },
    status: 1,
    createTime: "2024-01-01 10:00:00"
  },
  {
    id: 3,
    name: "生石花",
    scientificName: "Lithops",
    category: "番杏科",
    rarity: "传说",
    image:
      "https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",
    description: "形似石头的奇特多肉，极其稀有",
    growthCycle: 90,
    maxLevel: 20,
    baseEnergy: 500,
    unlockCondition: "连续冥想100天",
    attributes: {
      beauty: 95,
      hardiness: 95,
      growth: 20
    },
    status: 1,
    createTime: "2024-01-01 10:00:00"
  }
];

const categoryOptions = [
  { label: "景天科", value: "景天科" },
  { label: "百合科", value: "百合科" },
  { label: "番杏科", value: "番杏科" },
  { label: "大戟科", value: "大戟科" },
  { label: "龙舌兰科", value: "龙舌兰科" }
];

const rarityOptions = [
  { label: "普通", value: "普通" },
  { label: "稀有", value: "稀有" },
  { label: "史诗", value: "史诗" },
  { label: "传说", value: "传说" }
];

const statusOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 }
];

const columns = [
  {
    label: "图片",
    prop: "image",
    minWidth: 80
  },
  {
    label: "名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "学名",
    prop: "scientificName",
    minWidth: 180
  },
  {
    label: "科属",
    prop: "category",
    minWidth: 100
  },
  {
    label: "稀有度",
    prop: "rarity",
    minWidth: 100
  },
  {
    label: "成长周期",
    prop: "growthCycle",
    minWidth: 100
  },
  {
    label: "最大等级",
    prop: "maxLevel",
    minWidth: 100
  },
  {
    label: "基础能量",
    prop: "baseEnergy",
    minWidth: 100
  },
  {
    label: "属性",
    prop: "attributes",
    minWidth: 150
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

const onSearch = () => {
  loading.value = true;
  setTimeout(() => {
    tableData.value = mockData.filter(item => {
      return (
        (!searchForm.name || item.name.includes(searchForm.name)) &&
        (!searchForm.category || item.category === searchForm.category) &&
        (!searchForm.rarity || item.rarity === searchForm.rarity) &&
        (!searchForm.status || item.status.toString() === searchForm.status)
      );
    });
    loading.value = false;
  }, 500);
};

const resetForm = () => {
  searchForm.name = "";
  searchForm.category = "";
  searchForm.rarity = "";
  searchForm.status = "";
  onSearch();
};

const handleAdd = () => {
  dialogTitle.value = "新增品种";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

const handleDetail = (row: Variety) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

const handleEdit = (row: Variety) => {
  dialogTitle.value = "编辑品种";
  isEdit.value = true;
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleDelete = (row: Variety) => {
  ElMessageBox.confirm(`确定要删除品种 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    // 暂时只实现样式，不实际删除
    ElMessage.success("删除成功（演示）");
  });
};

const handleToggleStatus = (row: Variety) => {
  const action = row.status === 1 ? "禁用" : "启用";
  ElMessageBox.confirm(`确定要${action}品种 ${row.name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    row.status = row.status === 1 ? 0 : 1;
    ElMessage.success(`${action}成功`);
  });
};

const resetFormData = () => {
  formData.id = 0;
  formData.name = "";
  formData.scientificName = "";
  formData.category = "景天科";
  formData.rarity = "普通";
  formData.image = "";
  formData.description = "";
  formData.growthCycle = 30;
  formData.maxLevel = 10;
  formData.baseEnergy = 100;
  formData.unlockCondition = "";
  formData.attributes = {
    beauty: 50,
    hardiness: 50,
    growth: 50
  };
  formData.status = 1;
};

const handleSubmit = () => {
  if (!formData.name) {
    ElMessage.warning("请输入品种名称");
    return;
  }

  if (isEdit.value) {
    const index = tableData.value.findIndex(item => item.id === formData.id);
    if (index > -1) {
      tableData.value[index] = {
        ...formData,
        createTime: tableData.value[index].createTime
      };
      ElMessage.success("编辑成功");
    }
  } else {
    const newVariety: Variety = {
      ...formData,
      id: Date.now(),
      createTime: new Date().toLocaleString()
    };
    tableData.value.push(newVariety);
    ElMessage.success("新增成功");
  }

  dialogVisible.value = false;
};

const getRarityTag = (rarity: string) => {
  const rarityMap = {
    普通: { type: "info", color: "#909399" },
    稀有: { type: "success", color: "#67C23A" },
    史诗: { type: "warning", color: "#E6A23C" },
    传说: { type: "danger", color: "#F56C6C" }
  };
  return rarityMap[rarity] || { type: "info", color: "#909399" };
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="品种名称：" prop="name">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入品种名称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item label="科属：" prop="category">
        <el-select
          v-model="searchForm.category"
          placeholder="请选择科属"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="稀有度：" prop="rarity">
        <el-select
          v-model="searchForm.rarity"
          placeholder="请选择稀有度"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in rarityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="!w-[120px]"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="多肉品种管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增品种
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #image="{ row }">
            <el-image
              :src="row.image"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 8px"
              :preview-src-list="[row.image]"
              preview-teleported
            />
          </template>

          <template #rarity="{ row }">
            <el-tag :type="getRarityTag(row.rarity).type">
              {{ row.rarity }}
            </el-tag>
          </template>

          <template #growthCycle="{ row }"> {{ row.growthCycle }}天 </template>

          <template #attributes="{ row }">
            <div class="text-sm">
              <div>美观: {{ row.attributes.beauty }}</div>
              <div>耐性: {{ row.attributes.hardiness }}</div>
              <div>成长: {{ row.attributes.growth }}</div>
            </div>
          </template>

          <template #status="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      draggable
    >
      <el-form :model="formData" label-width="120px" label-position="right">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品种名称" required>
              <el-input
                v-model="formData.name"
                placeholder="请输入品种名称"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学名">
              <el-input
                v-model="formData.scientificName"
                placeholder="请输入学名"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="科属">
              <el-select
                v-model="formData.category"
                placeholder="请选择科属"
                style="width: 100%"
              >
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="稀有度">
              <el-select
                v-model="formData.rarity"
                placeholder="请选择稀有度"
                style="width: 100%"
              >
                <el-option
                  v-for="item in rarityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="图片链接">
          <el-input v-model="formData.image" placeholder="请输入图片链接" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入品种描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="成长周期">
              <el-input-number
                v-model="formData.growthCycle"
                :min="1"
                :max="365"
                controls-position="right"
                style="width: 100%"
              />
              <span class="ml-2 text-gray-500">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大等级">
              <el-input-number
                v-model="formData.maxLevel"
                :min="1"
                :max="50"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基础能量">
              <el-input-number
                v-model="formData.baseEnergy"
                :min="1"
                :max="1000"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="解锁条件">
          <el-input
            v-model="formData.unlockCondition"
            placeholder="请输入解锁条件"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="属性设置">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="mb-2">美观度: {{ formData.attributes.beauty }}</div>
              <el-slider
                v-model="formData.attributes.beauty"
                :min="1"
                :max="100"
                show-stops
              />
            </el-col>
            <el-col :span="8">
              <div class="mb-2">耐性: {{ formData.attributes.hardiness }}</div>
              <el-slider
                v-model="formData.attributes.hardiness"
                :min="1"
                :max="100"
                show-stops
              />
            </el-col>
            <el-col :span="8">
              <div class="mb-2">成长性: {{ formData.attributes.growth }}</div>
              <el-slider
                v-model="formData.attributes.growth"
                :min="1"
                :max="100"
                show-stops
              />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
