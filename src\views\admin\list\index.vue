<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import {
  getAdminList,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  type Admin,
  type CreateAdminParams,
  type UpdateAdminParams
} from "@/api/admin";

defineOptions({
  name: "AdminList"
});

const loading = ref(false);
const tableData = ref<Admin[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

// 搜索表单
const searchForm = reactive({
  search: ""
});

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const detailDialogVisible = ref(false);
const currentRow = ref<Admin>();

// 表单数据
const formData = reactive<CreateAdminParams & { id?: string }>({
  username: "",
  password: "",
  real_name: "",
  email: "",
  phone: "",
  role: "editor"
});

// 表格列配置
const columns: TableColumnList = [
  {
    label: "ID",
    prop: "id",
    width: 80
  },
  {
    label: "用户名",
    prop: "username",
    minWidth: 120
  },
  {
    label: "真实姓名",
    prop: "real_name",
    minWidth: 120
  },
  {
    label: "邮箱",
    prop: "email",
    minWidth: 180
  },
  {
    label: "手机号",
    prop: "phone",
    minWidth: 120
  },
  {
    label: "角色",
    prop: "role",
    minWidth: 100,
    cellRenderer: ({ row }) => {
      const roleMap = {
        super_admin: { text: "超级管理员", type: "danger" },
        admin: { text: "管理员", type: "warning" },
        editor: { text: "编辑员", type: "info" }
      };
      const role = roleMap[row.role] || { text: row.role, type: "info" };
      return (
        <el-tag type={role.type} size="small">
          {role.text}
        </el-tag>
      );
    }
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80,
    cellRenderer: ({ row }) => (
      <el-tag
        type={row.status === "active" ? "success" : "danger"}
        size="small"
      >
        {row.status === "active" ? "正常" : "禁用"}
      </el-tag>
    )
  },
  {
    label: "最后登录",
    prop: "last_login_at",
    minWidth: 160,
    formatter: ({ last_login_at }) =>
      last_login_at ? new Date(last_login_at).toLocaleString() : "-"
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160,
    formatter: ({ created_at }) => new Date(created_at).toLocaleString()
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getAdminList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      search: searchForm.search || undefined
    });

    if (response.code === 200) {
      tableData.value = response.data.items;
      pagination.total = response.data.total;
    } else {
      ElMessage.error("获取管理员列表失败");
    }
  } catch (error) {
    console.error("获取管理员列表失败:", error);
    ElMessage.error("获取管理员列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  loadData();
};

// 重置搜索
const resetForm = () => {
  searchForm.search = "";
  onSearch();
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    username: "",
    password: "",
    real_name: "",
    email: "",
    phone: "",
    role: "editor"
  });
  delete formData.id;
};

// 新增管理员
const handleAdd = () => {
  dialogTitle.value = "新增管理员";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: Admin) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

// 编辑管理员
const handleEdit = (row: Admin) => {
  dialogTitle.value = "编辑管理员";
  isEdit.value = true;
  Object.assign(formData, {
    id: row.id,
    username: row.username,
    real_name: row.real_name,
    email: row.email || "",
    phone: row.phone || "",
    role: row.role,
    password: "" // 编辑时不显示密码
  });
  dialogVisible.value = true;
};

// 删除管理员
const handleDelete = (row: Admin) => {
  ElMessageBox.confirm(`确定要删除管理员 ${row.real_name} 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      const response = await deleteAdmin(row.id);
      if (response.code === 200) {
        ElMessage.success("删除成功");
        loadData();
      } else {
        ElMessage.error(response.message || "删除失败");
      }
    } catch (error) {
      console.error("删除管理员失败:", error);
      ElMessage.error("删除失败");
    }
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formData.real_name) {
    ElMessage.warning("请输入真实姓名");
    return;
  }
  if (!formData.username) {
    ElMessage.warning("请输入用户名");
    return;
  }
  if (!isEdit.value && !formData.password) {
    ElMessage.warning("请输入密码");
    return;
  }

  try {
    if (isEdit.value) {
      // 编辑管理员
      const updateData: UpdateAdminParams = {
        real_name: formData.real_name,
        email: formData.email || undefined,
        phone: formData.phone || undefined,
        role: formData.role
      };
      const response = await updateAdmin(formData.id!, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.message || "编辑失败");
      }
    } else {
      // 新增管理员
      const createData: CreateAdminParams = {
        username: formData.username,
        password: formData.password,
        real_name: formData.real_name,
        email: formData.email || undefined,
        phone: formData.phone || undefined,
        role: formData.role
      };
      const response = await createAdmin(createData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.message || "新增失败");
      }
    }
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error("操作失败");
  }
};

// 分页相关
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索关键词：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入用户名或真实姓名"
          clearable
          class="!w-[200px]"
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="管理员列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增管理员
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      draggable
    >
      <el-form
        ref="dialogFormRef"
        :model="formData"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="formData.username"
            placeholder="请输入用户名"
            :disabled="isEdit"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item v-if="!isEdit" label="密码" prop="password">
          <el-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            maxlength="50"
            show-password
          />
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input
            v-model="formData.real_name"
            placeholder="请输入真实姓名"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="请输入手机号"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="formData.role" placeholder="请选择角色">
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="编辑员" value="editor" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
